<!doctype html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" href="/icon.svg" sizes="any" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Meta tags -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="ImageConverter Pro - Professional image conversion tool. Convert JPEG, PNG, WebP, GIF, BMP with HD quality preservation. Unlimited batch processing, drag & drop interface." />
    <meta name="keywords" content="image converter, photo converter, JPEG, PNG, WebP, GIF, BMP, batch conversion, image optimization" />
    <meta name="author" content="ImageConverter Pro" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="ImageConverter Pro - Professional Image Conversion Tool" />
    <meta property="og:description" content="Convert images between JPEG, PNG, WebP, GIF, BMP formats with HD quality preservation. Unlimited batch processing with drag & drop interface." />
    <meta property="og:image" content="/icon.svg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="ImageConverter Pro - Professional Image Conversion Tool" />
    <meta property="twitter:description" content="Convert images between JPEG, PNG, WebP, GIF, BMP formats with HD quality preservation. Unlimited batch processing with drag & drop interface." />
    <meta property="twitter:image" content="/icon.svg" />

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="msapplication-TileColor" content="#3b82f6" />

    <title>ImageConverter Pro - Professional Image Conversion Tool</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
