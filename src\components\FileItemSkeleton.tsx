import React from 'react';
import { Skeleton } from './ui/shadcn/skeleton';

const FileItemSkeleton: React.FC = () => {
  return (
    <div className="file-card">
      <div className="flex items-start space-x-3">
        {/* Image Preview Skeleton */}
        <div className="flex-shrink-0">
          <Skeleton className="w-16 h-16 rounded-lg" />
        </div>
        
        {/* File Info Skeleton */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0 space-y-2">
              {/* File name */}
              <Skeleton className="h-4 w-3/4" />
              
              {/* Badges */}
              <div className="flex items-center space-x-2">
                <Skeleton className="h-5 w-12" />
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
            
            {/* Status icon */}
            <div className="flex items-center space-x-2 ml-2">
              <Skeleton className="w-5 h-5 rounded-full" />
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="mt-3">
            <Skeleton className="h-2 w-full" />
          </div>
          
          {/* Action buttons */}
          <div className="mt-3">
            <div className="flex space-x-2">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 flex-1" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileItemSkeleton;
