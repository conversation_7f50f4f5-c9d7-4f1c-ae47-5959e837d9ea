@import './styles/design-system.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* shadcn/ui color variables */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
  }

  .dark {
    /* shadcn/ui dark mode color variables */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    background-color: var(--color-bg-secondary);
    color: var(--color-text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--color-bg-tertiary);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--color-border-secondary);
    border-radius: var(--radius-sm);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--color-text-tertiary);
  }
}

@layer components {
  /* Enhanced drag area with better visual feedback */
  .drag-area {
    border: 2px dashed var(--color-border-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    background-color: var(--color-bg-primary);
    position: relative;
    overflow: hidden;
  }

  .drag-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(59, 130, 246, 0.1),
      transparent
    );
    transition: left var(--transition-slow);
  }

  .drag-area.drag-over {
    border-color: var(--color-primary-500);
    background-color: var(--color-primary-50);
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    border-style: solid;
  }

  .dark .drag-area.drag-over {
    background-color: rgb(59 130 246 / 0.1);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
  }

  .drag-area.drag-over::before {
    left: 100%;
  }

  .drag-area:hover:not(.drag-over) {
    border-color: var(--color-primary-300);
    background-color: var(--color-bg-secondary);
    transform: translateY(-2px);
  }

  /* Enhanced file card with better hover effects */
  .file-card {
    background-color: var(--color-bg-primary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
  }

  .file-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(59, 130, 246, 0.05),
      transparent
    );
    transition: left 0.5s ease;
  }

  .file-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--color-primary-300);
  }

  .file-card:hover::before {
    left: 100%;
  }

  /* Enhanced progress bar with gradient and animation */
  .progress-bar {
    width: 100%;
    background-color: var(--color-bg-tertiary);
    border-radius: var(--radius-sm);
    height: 8px;
    overflow: hidden;
    position: relative;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg,
      var(--color-primary-500),
      var(--color-primary-400),
      var(--color-primary-500)
    );
    background-size: 200% 100%;
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal) ease-out;
    animation: progressShine 2s infinite;
  }

  @keyframes progressShine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Status indicators */
  .status-success {
    color: var(--color-success-600);
    background-color: var(--color-success-50);
  }

  .status-warning {
    color: var(--color-warning-600);
    background-color: var(--color-warning-50);
  }

  .status-error {
    color: var(--color-error-600);
    background-color: var(--color-error-50);
  }

  /* Enhanced button styles */
  .btn-primary {
    @extend .button-base;
    background-color: var(--color-primary-600);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-sm);
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .btn-primary:active:not(:disabled) {
    transform: translateY(0);
  }

  .btn-secondary {
    @extend .button-base;
    background-color: var(--color-bg-secondary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border-primary);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-sm);
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: var(--color-bg-tertiary);
    border-color: var(--color-border-secondary);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced micro-animations */
  .animate-bounce-subtle {
    animation: bounceSubtle 0.6s ease-in-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Improved hover effects */
  .hover-lift {
    transition: all 0.2s ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .hover-lift:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* Professional Header Styles */
  .header-gradient {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 100%);
  }

  .dark .header-gradient {
    background: linear-gradient(135deg,
      rgba(17, 24, 39, 0.95) 0%,
      rgba(17, 24, 39, 0.9) 100%);
  }

  .logo-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .dark .logo-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }

  /* Feature badge animations */
  .feature-badge {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .feature-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .feature-badge:hover::before {
    left: 100%;
  }

  .feature-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  /* Breadcrumb hover effects */
  .breadcrumb-item {
    transition: all 0.2s ease;
    position: relative;
  }

  .breadcrumb-item:hover {
    transform: translateY(-1px);
  }

  .breadcrumb-item::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--color-primary-500);
    transition: width 0.3s ease;
  }

  .breadcrumb-item:hover::after {
    width: 100%;
  }

  /* Stats counter animations */
  .stats-counter {
    animation: countUp 2s ease-out;
  }

  @keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Professional header shadow */
  .header-shadow {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  .dark .header-shadow {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  /* Enhanced badge styles */
  .version-badge {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
  }

  .dark .version-badge {
    background: linear-gradient(135deg, #065f46, #047857);
  }
}
